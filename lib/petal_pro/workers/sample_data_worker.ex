defmodule PetalPro.Workers.SampleDataWorker do
  @moduledoc """
  Example of how to do async work with <PERSON><PERSON>.

  Run with:
  Oban.insert(PetalPro.Workers.SampleDataWorker.new(%{}))
  """
  use Oban.Worker, queue: :default

  alias PetalPro.Accounts.User
  alias PetalPro.Accounts.UserSeeder
  alias PetalPro.Accounts.UserToken
  alias PetalPro.Accounts.UserTOTP
  alias PetalPro.Files.File
  alias PetalPro.Files.FileSeeder
  alias PetalPro.Logs.Log
  alias PetalPro.Orgs.Invitation
  alias PetalPro.Orgs.Membership
  alias PetalPro.Orgs.Org
  alias PetalPro.Orgs.OrgSeeder
  alias PetalPro.Posts.Post
  alias PetalPro.Posts.PostSeeder

  require Logger

  @impl Oban.Worker
  def perform(%Oban.Job{} = _job) do
    Logger.info("Running SampleDataWorker")
    PetalPro.Repo.delete_all(File)
    PetalPro.Repo.delete_all(Post)
    PetalPro.Repo.delete_all(Log)
    PetalPro.Repo.delete_all(UserTOTP)
    PetalPro.Repo.delete_all(Invitation)
    PetalPro.Repo.delete_all(Membership)
    PetalPro.Repo.delete_all(Org)
    PetalPro.Repo.delete_all(UserToken)
    PetalPro.Repo.delete_all(User)

    admin = UserSeeder.admin()

    normal_user =
      UserSeeder.normal_user(%{
        email: "<EMAIL>",
        name: "Sarah Cunningham",
        password: "password",
        confirmed_at: Timex.to_naive_datetime(DateTime.utc_now())
      })

    UserSeeder.fake_subscription(normal_user)

    org = OrgSeeder.random_org(admin)
    PetalPro.Orgs.create_invitation(org, %{email: normal_user.email})

    UserSeeder.random_users(20)

    FileSeeder.create_files(admin)
    PostSeeder.create_posts(admin)

    Logger.info("Running SampleDataWorker Complete")

    :ok
  end
end
