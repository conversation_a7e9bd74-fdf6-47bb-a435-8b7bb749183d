# fly.toml app configuration file generated for petal-pro-demo on 2023-10-25T11:57:01+11:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = "petal-pro-demo"
primary_region = "bos"
kill_signal = "SIGTERM"
kill_timeout = "5s"
swap_size_mb = 512

[experimental]
  auto_rollback = true

[build]

[deploy]
  release_command = "/app/bin/migrate"

[env]
  PHX_HOST = "petal-pro-demo.fly.dev"
  PORT = "8080"

[[services]]
  protocol = "tcp"
  internal_port = 8080
  processes = ["app"]

  [[services.ports]]
    port = 80
    handlers = ["http"]
    force_https = true

  [[services.ports]]
    port = 443
    handlers = ["tls", "http"]
  [services.concurrency]
    type = "connections"
    hard_limit = 1000
    soft_limit = 1000

  [[services.tcp_checks]]
    interval = "15s"
    timeout = "2s"
    grace_period = "1s"
